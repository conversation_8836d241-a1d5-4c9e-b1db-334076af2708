<!--表单表格列 -->
<template>
  <el-table-column
    :label="column.label"
    :prop="column.prop"
    :width="column.width"
    :min-width="column.minWidth || 120"
    :fixed="column.fixed"
    :align="column.align || 'center'"
  >
    <!-- 如果有子列，递归渲染 -->
    <template v-if="column.children && column.children.length">
      <OgwColumn
        v-for="child in column.children"
        :key="child.prop"
        :column="child"
        :show-index="showIndex"
        :summary-config="summaryConfig"
        :fixed="column.fixed"
        @enter-edit="$emit('enter-edit', $event)"
        @exit-edit="$emit('exit-edit', $event)"
        @cell-change="$emit('cell-change', $event)"
        @cell-click="$emit('cell-click', $event)"
      >
        <!-- 透传插槽 -->
        <template v-for="(_, slotName) in $scopedSlots" #[slotName]="scope">
          <slot :name="slotName" v-bind="scope" />
        </template>
      </OgwColumn>
    </template>

    <!-- 没有子列，渲染单元格内容 -->
    <template v-if="!column.children" v-slot="scope">
      <template v-if="scope.row._isSummaryRow">
        <template
          v-if="isFirstSummaryTextColumn(scope.row._summaryType, column.prop)"
        >
          <span class="summary-text">{{ scope.row._summaryText }}</span>
        </template>
        <template v-else-if="summaryConfig.sumColumns.includes(column.prop)">
          <span class="summary-value">{{ scope.row[column.prop] }}</span>
        </template>
      </template>

      <template v-else>
        <!-- 编辑模式 -->
        <template
          v-if="column.editable && isEditingCell(scope.$index, column.prop)"
        >
          <div class="editing-input" @click.stop>
            <!-- 下拉选择器 -->
            <el-select
              v-if="column.options"
              v-model="scope.row[column.prop]"
              size="small"
              style="width: 100%"
              @change="handleEditChange(scope.row, column.prop, scope.$index, $event)"
              @blur="handleEditBlur"
            >
              <el-option
                v-for="opt in column.options"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
            <!-- 日期选择器 -->
            <el-date-picker
              v-else-if="column.editType === 'date'"
              ref="datePicker"
              v-model="scope.row[column.prop]"
              :type="getDatePickerType(column)"
              :format="getDateFormat(column)"
              :value-format="getDateValueFormat(column)"
              :placeholder="getDatePlaceholder(column)"
              size="small"
              style="width: 100%"
              :clearable="false"
              :append-to-body="true"
              :popper-class="'table-date-picker'"
              @change="handleEditChange(scope.row, column.prop, scope.$index, $event)"
              @blur="handleDatePickerBlur"
              @focus="handleDatePickerFocus"
            />
            <!-- 输入框编辑组件 -->
            <div v-else class="input-wrapper">
              <component
                :is="getEditComponent(column)"
                v-model="scope.row[column.prop]"
                size="small"
                :class="getInputClass(column, scope.row[column.prop])"
                @blur="handleInputBlur(scope.row, column.prop, scope.$index)"
                @keyup.enter="handleEditEnter"
                @input="handleInputChange(scope.row, column.prop, scope.$index, $event)"
                @keypress="handleKeyPress(column, $event)"
                style="width: 100%"
              />
              <!-- 验证错误提示 -->
              <div
                v-if="getValidationError(column, scope.row[column.prop])"
                class="validation-error"
              >
                {{ getValidationError(column, scope.row[column.prop]) }}
              </div>
            </div>
          </div>
        </template>

        <!-- 可编辑但未在编辑状态 -->
        <template v-else-if="column.editable">
          <div
            :class="[
              'editable-cell',
              { 'edit-success': isEditSuccess(scope.$index, column.prop) }
            ]"
            @click="handleEditableClick($event, scope.$index, column.prop, scope.row)"
            @mouseenter="handleCellHover(true)"
            @mouseleave="handleCellHover(false)"
          >
            <span class="cell-content">
              {{ getFormattedValue(column, scope.row) || "-" }}
            </span>
            <i class="el-icon-edit edit-icon"></i>
          </div>
        </template>

        <!-- 可点击但不可编辑 -->
        <template v-else-if="column.clickable">
          <span
            class="clickable-cell"
            @click="handleClickableClick($event, scope.row, column.prop, scope.$index)"
          >
            <slot :name="column.prop" v-bind="scope">
              {{ getFormattedValue(column, scope.row) }}
            </slot>
          </span>
        </template>

        <!-- 普通单元格 -->
        <template v-else>
          <span>
            <slot :name="column.prop" v-bind="scope">
              {{ getFormattedValue(column, scope.row) }}
            </slot>
          </span>
        </template>
      </template>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: "OgwColumn",
  props: {
    column: Object,
    showIndex: Boolean,
    summaryConfig: Object,
    editingCell: {
      type: Object,
      default: () => ({ rowIndex: null, prop: null }),
    },
  },
  data() {
    return {
      originalValue: null, // 存储编辑前的原始值
      currentEditingKey: null, // 当前编辑的单元格标识
      isHovering: false, // 鼠标悬停状态
      editSuccessKey: null, // 编辑成功的单元格标识
    };
  },
  watch: {
    editingCell: {
      handler(newVal, oldVal) {
        // 监听编辑状态变化
        const newKey = newVal.rowIndex !== null ? `${newVal.rowIndex}-${newVal.prop}` : null;
        const oldKey = oldVal && oldVal.rowIndex !== null ? `${oldVal.rowIndex}-${oldVal.prop}` : null;

        if (oldKey && oldKey !== newKey && this.currentEditingKey === oldKey) {
          // 编辑状态结束，且是当前组件负责的编辑
          // 如果有原始值，说明可能是取消编辑，需要回滚
          if (this.originalValue !== null) {
            // 这里可以选择是否回滚，暂时不自动回滚，让用户决定
            this.originalValue = null;
          }
          this.currentEditingKey = null;
        }

        if (newKey && newKey !== oldKey) {
          // 新的编辑开始
          const currentKey = `${newVal.rowIndex}-${this.column.prop}`;
          if (newKey === currentKey) {
            this.currentEditingKey = newKey;
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    isFirstSummaryTextColumn(type, prop) {
      const mergeCols =
        type === "grand"
          ? this.summaryConfig.grandTotalTextMergeColumns
          : type === "sub"
          ? this.summaryConfig.subTotalTextMergeColumns
          : type === "customSub"
          ? this.summaryConfig.customSubTotalTextMergeColumns
          : type === "customGrand"
          ? this.summaryConfig.customGrandTotalTextMergeColumns
          : [];
      return mergeCols.length && mergeCols[0] === prop;
    },
    isEditingCell(index, prop) {
      return (
        this.editingCell.rowIndex === index && this.editingCell.prop === prop
      );
    },
    getFormattedValue(col, row) {
      const value = row[col.prop];
      if (col.formatter) {
        return col.formatter(row, value);
      }
      if (col.options) {
        const match = col.options.find((opt) => opt.value === value);
        return match ? match.label : value;
      }
      // 日期类型的格式化显示
      if (col.editType === 'date' && value) {
        return this.formatDateValue(value, col);
      }
      return value;
    },
    formatDateValue(value, column) {
      if (!value) return '';

      try {
        const date = new Date(value);
        if (isNaN(date.getTime())) return value;

        const type = this.getDatePickerType(column);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return type === 'month' ? `${year}年${month}月` : `${year}-${month}-${day}`;
      } catch (error) {
        console.warn('日期格式化失败:', error);
        return value;
      }
    },
    getEditComponent(column) {
      // 根据列配置返回对应的编辑组件
      if (column.editComponent) {
        return column.editComponent;
      }
      // 默认使用 el-input
      return 'el-input';
    },
    // 日期选择器相关方法
    getDatePickerType(column) {
      return column.dateConfig?.type || 'date';
    },
    getDateFormat(column) {
      const type = this.getDatePickerType(column);
      if (column.dateConfig?.format) {
        return column.dateConfig.format;
      }
      // 默认格式
      return type === 'month' ? 'yyyy年MM月' : 'yyyy-MM-dd';
    },
    getDateValueFormat(column) {
      const type = this.getDatePickerType(column);
      if (column.dateConfig?.valueFormat) {
        return column.dateConfig.valueFormat;
      }
      // 默认值格式
      return type === 'month' ? 'yyyy-MM' : 'yyyy-MM-dd';
    },
    getDatePlaceholder(column) {
      const type = this.getDatePickerType(column);
      if (column.dateConfig?.placeholder) {
        return column.dateConfig.placeholder;
      }
      // 默认占位符
      return type === 'month' ? '请选择月份' : '请选择日期';
    },
    handleEditableClick(event, index, prop, row) {
      // 阻止事件冒泡
      event.stopPropagation();
      // 保存原始值，用于验证失败时回滚
      this.originalValue = row[prop];
      // 进入编辑模式
      this.$emit('enter-edit', { index, prop });
    },
    handleClickableClick(event, row, prop, index) {
      // 阻止事件冒泡
      event.stopPropagation();
      // 发出点击事件
      this.$emit('cell-click', {
        event,
        row,
        prop,
        index,
      });
    },
    handleEditChange(row, prop, index, value) {
      // 获取列配置
      const column = this.column;

      // 如果是日期类型，进行验证
      if (column.editType === 'date' && value) {
        const validatedValue = this.validateDateValue(value, column);
        if (validatedValue !== value) {
          // 如果验证后的值不同，更新行数据
          row[prop] = validatedValue;
          value = validatedValue;
        }
      }

      // 发出值变化事件
      this.$emit('cell-change', {
        row,
        prop,
        index,
        value,
      });

      // 显示编辑成功反馈
      this.showEditSuccess(index, prop);
    },
    validateDateValue(value, column) {
      if (!value) return value;

      try {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          console.warn('无效的日期值:', value);
          return '';
        }

        // 检查日期范围限制（如果配置了的话）
        if (column.dateConfig?.minDate) {
          const minDate = new Date(column.dateConfig.minDate);
          if (date < minDate) {
            console.warn('日期不能早于最小日期:', column.dateConfig.minDate);
            return column.dateConfig.minDate;
          }
        }

        if (column.dateConfig?.maxDate) {
          const maxDate = new Date(column.dateConfig.maxDate);
          if (date > maxDate) {
            console.warn('日期不能晚于最大日期:', column.dateConfig.maxDate);
            return column.dateConfig.maxDate;
          }
        }

        return value;
      } catch (error) {
        console.warn('日期验证失败:', error);
        return '';
      }
    },
    handleEditBlur() {
      // 延迟退出编辑，避免与点击事件冲突
      setTimeout(() => {
        this.$emit('exit-edit');
      }, 150);
    },
    handleEditEnter() {
      // 按回车键退出编辑，需要先进行验证
      // 获取当前编辑的单元格信息
      const editingCell = this.$parent.editingCell || this.$parent.$parent.editingCell;
      if (!editingCell || editingCell.rowIndex === null) {
        this.$emit('exit-edit');
        return;
      }

      // 获取当前行数据
      const tableData = this.$parent.tableWithSummaries || this.$parent.$parent.tableWithSummaries;
      if (!tableData || !tableData[editingCell.rowIndex]) {
        this.$emit('exit-edit');
        return;
      }

      const row = tableData[editingCell.rowIndex];
      const prop = editingCell.prop;
      const value = row[prop];

      // 如果有验证规则，进行验证
      if (this.column.validation) {
        const validation = this.validateInput(this.column, value);
        if (validation.valid) {
          // 验证通过，发出变化事件并退出编辑
          this.$emit('cell-change', {
            row,
            prop,
            index: editingCell.rowIndex,
            value,
          });
          // 清理原始值
          this.originalValue = null;
          this.$emit('exit-edit');
        } else {
          // 验证失败，回滚到原始值，不退出编辑模式
          console.warn('输入验证失败:', validation.message);
          // 回滚数据到原始值
          if (this.originalValue !== null) {
            row[prop] = this.originalValue;
          }
          // 强制更新视图
          this.$forceUpdate();
          // 不退出编辑模式
          return;
        }
      } else {
        // 没有验证规则，直接退出编辑
        // 清理原始值
        this.originalValue = null;
        this.$emit('exit-edit');
      }
    },
    handleDatePickerFocus() {
      // 日期选择器获得焦点时的处理
      this.$nextTick(() => {
        // 确保弹出层正确显示
        const datePicker = this.$refs.datePicker;
        if (datePicker && datePicker.focus) {
          datePicker.focus();
        }
      });
    },
    handleDatePickerBlur() {
      // 日期选择器失去焦点时的处理
      // 延迟退出编辑，避免与日期选择器弹出层点击事件冲突
      setTimeout(() => {
        this.$emit('exit-edit');
      }, 200);
    },
    // 输入验证相关方法
    validateInput(column, value) {
      if (!column.validation) return { valid: true, message: '' };

      const validation = column.validation;
      const stringValue = String(value || '');

      // 必填验证
      if (validation.required && !stringValue.trim()) {
        return {
          valid: false,
          message: validation.errorMessage || '此字段为必填项'
        };
      }

      // 如果值为空且非必填，则通过验证
      if (!stringValue.trim() && !validation.required) {
        return { valid: true, message: '' };
      }

      // 根据类型进行验证
      switch (validation.type) {
        case 'number':
          return this.validateNumber(stringValue, validation);
        case 'decimal':
          return this.validateDecimal(stringValue, validation);
        case 'regex':
          return this.validateRegex(stringValue, validation);
        case 'text':
        default:
          return this.validateText(stringValue, validation);
      }
    },
    validateNumber(value, validation) {
      const numberRegex = /^-?\d+$/;
      if (!numberRegex.test(value)) {
        return {
          valid: false,
          message: validation.errorMessage || '请输入有效的整数'
        };
      }

      const num = parseInt(value);
      if (validation.min !== undefined && num < validation.min) {
        return {
          valid: false,
          message: validation.errorMessage || `数值不能小于 ${validation.min}`
        };
      }

      if (validation.max !== undefined && num > validation.max) {
        return {
          valid: false,
          message: validation.errorMessage || `数值不能大于 ${validation.max}`
        };
      }

      return { valid: true, message: '' };
    },
    validateDecimal(value, validation) {
      const decimalRegex = /^-?\d*\.?\d*$/;
      if (!decimalRegex.test(value)) {
        return {
          valid: false,
          message: validation.errorMessage || '请输入有效的数字'
        };
      }

      // 检查小数位数
      if (validation.precision !== undefined) {
        const decimalPart = value.split('.')[1];
        if (decimalPart && decimalPart.length > validation.precision) {
          return {
            valid: false,
            message: validation.errorMessage || `小数位数不能超过 ${validation.precision} 位`
          };
        }
      }

      const num = parseFloat(value);
      if (!isNaN(num)) {
        if (validation.min !== undefined && num < validation.min) {
          return {
            valid: false,
            message: validation.errorMessage || `数值不能小于 ${validation.min}`
          };
        }

        if (validation.max !== undefined && num > validation.max) {
          return {
            valid: false,
            message: validation.errorMessage || `数值不能大于 ${validation.max}`
          };
        }
      }

      return { valid: true, message: '' };
    },
    validateRegex(value, validation) {
      if (!validation.pattern) {
        return { valid: true, message: '' };
      }

      const regex = new RegExp(validation.pattern);
      if (!regex.test(value)) {
        return {
          valid: false,
          message: validation.errorMessage || '输入格式不正确'
        };
      }

      return { valid: true, message: '' };
    },
    validateText(value, validation) {
      // 文本长度验证
      if (validation.minLength !== undefined && value.length < validation.minLength) {
        return {
          valid: false,
          message: validation.errorMessage || `最少输入 ${validation.minLength} 个字符`
        };
      }

      if (validation.maxLength !== undefined && value.length > validation.maxLength) {
        return {
          valid: false,
          message: validation.errorMessage || `最多输入 ${validation.maxLength} 个字符`
        };
      }

      return { valid: true, message: '' };
    },
    // 输入事件处理方法
    handleInputChange(/* row, prop, index, value */) {
      // 实时验证（仅用于视觉反馈，不阻止输入）
      const column = this.column;
      if (column.validation) {
        // 触发重新渲染以更新样式
        this.$forceUpdate();
      }
    },
    handleInputBlur(row, prop, index) {
      // 失去焦点时进行完整验证
      const column = this.column;
      const value = row[prop];

      if (column.validation) {
        const validation = this.validateInput(column, value);
        if (validation.valid) {
          // 验证通过，发出变化事件并退出编辑
          this.$emit('cell-change', {
            row,
            prop,
            index,
            value,
          });
          // 清理原始值
          this.originalValue = null;
          this.handleEditBlur();
        } else {
          // 验证失败，回滚到原始值，不退出编辑模式
          console.warn('输入验证失败:', validation.message);
          // 回滚数据到原始值
          if (this.originalValue !== null) {
            row[prop] = this.originalValue;
          }
          // 强制更新视图
          this.$forceUpdate();
          // 不退出编辑模式，让用户继续编辑
          return;
        }
      } else {
        // 没有验证规则，直接处理
        this.$emit('cell-change', {
          row,
          prop,
          index,
          value,
        });
        // 清理原始值
        this.originalValue = null;
        this.handleEditBlur();
      }
    },
    handleKeyPress(column, event) {
      // 实时输入限制
      if (!column.validation) return;

      const validation = column.validation;
      const char = String.fromCharCode(event.charCode);

      switch (validation.type) {
        case 'number':
          // 只允许数字和负号
          if (!/[\d-]/.test(char)) {
            event.preventDefault();
          }
          break;
        case 'decimal':
          // 允许数字、小数点和负号
          if (!/[\d.-]/.test(char)) {
            event.preventDefault();
          }
          break;
        // text 和 regex 类型不限制实时输入
      }
    },
    getInputClass(column, value) {
      if (!column.validation) return '';

      const validation = this.validateInput(column, value);
      return validation.valid ? 'input-valid' : 'input-invalid';
    },
    getValidationError(column, value) {
      if (!column.validation) return '';

      const validation = this.validateInput(column, value);
      return validation.valid ? '' : validation.message;
    },
    handleCellHover(isHovering) {
      this.isHovering = isHovering;
    },
    isEditSuccess(index, prop) {
      return this.editSuccessKey === `${index}-${prop}`;
    },
    showEditSuccess(index, prop) {
      this.editSuccessKey = `${index}-${prop}`;
      // 1.5秒后清除成功状态
      setTimeout(() => {
        if (this.editSuccessKey === `${index}-${prop}`) {
          this.editSuccessKey = null;
        }
      }, 1500);
    },
  },
};
</script>

<style lang="scss" scoped>
.summary-cell {
  font-weight: bold;
  color: #606266;
}

.clickable-cell {
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;
}

.editable-cell {
  position: relative;
  cursor: pointer;
  color: #409eff;
  border: 1px solid transparent;
  border-radius: 4px;
  padding: 4px 8px;
  margin: -4px -8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 24px;

  .cell-content {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .edit-icon {
    opacity: 0;
    margin-left: 8px;
    font-size: 12px;
    color: #909399;
    transition: opacity 0.2s ease;
    flex-shrink: 0;
  }

  &:hover {
    background-color: #ecf5ff;
    border-color: #b3d8ff;

    .edit-icon {
      opacity: 1;
    }
  }

  &:active {
    background-color: #d9ecff;
    border-color: #409eff;
  }
}

// 编辑状态下的样式
::v-deep .editing-input {
  position: relative;

  // 编辑状态指示器
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid #409eff;
    border-radius: 6px;
    pointer-events: none;
    z-index: 1;
    animation: editingPulse 2s infinite;
  }

  .el-input,
  .el-select,
  .el-date-editor {
    .el-input__inner {
      border-color: #409eff !important;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
      background-color: #fff !important;
    }
  }

  .el-date-editor {
    .el-input__prefix {
      color: #409eff;
    }
  }

  // 输入框验证样式
  .input-wrapper {
    position: relative;

    .input-valid {
      .el-input__inner {
        border-color: #67c23a;
        box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
      }
    }

    .input-invalid {
      .el-input__inner {
        border-color: #f56c6c;
        box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
      }
    }

    .validation-error {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background-color: #f56c6c;
      color: #fff;
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 0 0 4px 4px;
      z-index: 1000;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// 编辑状态动画
@keyframes editingPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

// 编辑成功反馈动画
@keyframes editSuccess {
  0% {
    background-color: #f0f9ff;
    border-color: #67c23a;
  }
  100% {
    background-color: transparent;
    border-color: transparent;
  }
}

.edit-success {
  animation: editSuccess 1s ease-out;
}
</style>
