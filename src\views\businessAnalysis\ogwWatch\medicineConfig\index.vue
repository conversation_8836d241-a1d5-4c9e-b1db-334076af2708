<template>
  <div class="medicine-config">
    <OgwSearch
      :fields="searchFields"
      v-model="searchForm"
      @search="handleSearch"
      @reset="handleReset"
    ></OgwSearch>

    <div class="table-container">
      <div class="table-actions">
        <el-button type="primary" size="mini" @click="saveInfo">保存</el-button>
      </div>
      <OgwTable
        :loading="loading"
        :columns="columns"
        :data="tableData"
        :merge-keys="['productionName']"
        :show-actions="false"
        @cell-change="handleChange"
      >
        <template #instruction="{ row, $index }">
          <el-button type="text" size="mini" @click="deleteRow(row)">
            上传</el-button
          >
          <el-button type="text" size="mini" @click="checkInfo(row)"
            >查看</el-button
          ></template
        >
      </OgwTable>
    </div>
    <FileUpload
      :visible.sync="showUpload"
      :upload-url="'/api/chemicalConfig/upload'"
      :file-types="['.pdf', '.png', '.jpg']"
      :maxSizeMB="10"
      :fileData="fileData"
      @uploadFile="uploadFile"
    />
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import {
  getTableList,
  updateMedicineConfig,
} from "@/api/ogwWatch/medicineConfig.js";
import { getProd } from "@/api/common.js";
export default {
  name: "medicineConfig",
  components: {
    OgwSearch,
    OgwTable,
  },
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
    this.getTableListInfo();
    this.getProdList();
  },
  watch: {
    searchForm: {
      handler(val) {
        this.deviceOptions = this.orgList.map((item) => {
          if (item.orgId === val.orgId) {
            return item.children;
          }
        });
        this.searchFields[1].options = this.deviceOptions.flat(Infinity);
      },
      deep: true,
    },
  },
  computed: {
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [],
        },
      ];
    },
  },
  data() {
    return {
      loading: false,
      showUpload: false,
      hzNo: "",
      orgList: [],
      searchForm: {
        deptId: "",
        deviceNameCode: "",
      },
      columns: [
        { label: "平台名称", prop: "productionName" },
        { label: "药剂名称", prop: "chemicalName" , editable: true},
        { label: "药剂型号", prop: "chemicalType" , editable: true},
        { label: "库存编码", prop: "materialCode" , editable: true},
        { label: "推荐加注浓度(ppm)", prop: "reConcentration", editable: true },
        {
          label: "用途",
          prop: "purpose",
          editable: true,
          options: [
            { label: "原油处理", value: "4304030400" },
            { label: "污水处理", value: "4304030100" },
            { label: "注水处理", value: "4304030200" },
            { label: "天然气处理", value: "4304030300" },
            { label: "其他化学药品", value: "4304030500" },
          ],
        },
        {
          label: "费用关注度",
          prop: "attention",
          editable: true,
          options: [
            { label: "重点关注", value: 1 },
            { label: "一般关注", value: 2 },
          ],
        },
        { label: "加注浓度(ppm)", prop: "concentration" , editable: true},
        {
          label: "密度(kg/m³)",
          prop: "density",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入数字",
          },
        },
        { label: "药剂说明书", prop: "instruction" },
      ],
      tableData: [],
      updateData: [], //表格更新的数据
      fileData: {},
    };
  },
  methods: {
    handleSearch(value) {
      this.hzNo = value.deviceNameCode;
      this.getTableListInfo();
    },
    handleReset(value) {
      // 重置搜索表单
      this.searchForm = {
        deptId: "",
        deviceNameCode: "",
      };
      this.hzNo = "";
      this.getTableListInfo();
    },
    deleteRow(row) {
      this.showUpload = true;
      this.fileData = { id: row.id };
    },
    checkInfo(row) {
      const fileIds = row.fileInfo || null;

      this.$router.push({
        name: "opinionsPre",
        params: { fileIds },
      });
    },
    async getTableListInfo() {
      this.loading = true;
      try {
        const res = await getTableList(this.hzNo || "");
        if (res.code === 200) {
          this.tableData = res.data;
        }
      } catch (error) {
        console.log("error", error);
      } finally {
        this.loading = false;
      }
    },
    async saveInfo() {
      // set集合转成数组
      const targetArr = Array.from(new Set(this.updateData));

      const targetObj = targetArr.map((item) => this.tableData[item]);

      const res = await updateMedicineConfig(targetObj);
      // console.log("res", res);
      if (res.code === 200) {
        this.$message.success("保存成功");
      }
    },
    handleChange(row) {
      this.updateData.push(row.index);
    },
    uploadFile(file) {
      console.log("file", file);
    },
    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.name,
          })),
        }));
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.medicine-config {
  padding: 20px;
  background-color: #fff;
}
.table-container {
  margin-top: 20px;
  .table-actions {
    margin-bottom: 10px;
    text-align: right;
  }
}
</style>
