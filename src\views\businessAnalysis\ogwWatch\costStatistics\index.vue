<template>
  <div class="cost-statistics">
    <div class="search-header">
      <OgwSearch
        :fields="searchFields"
        v-model="searchForm"
        @search="handleSearch"
        @reset="handleReset"
      ></OgwSearch>
      <div class="group-btn">
        <el-button type="primary" disabled>报告生成</el-button>
        <el-button type="primary" disabled>导出</el-button>
      </div>
    </div>
    <div>
      <div class="title">
        陵水-岸城作业公司油气水处理年度费用执行报表(截止{{ date }})
      </div>
      <div class="table-title">
        陵水-座城作业公司化学药剂费用执行(截止{{ date }})
      </div>
      <OgwTable
        :data="exTableData"
        :columns="exColumns"
        :summary-config="exSummaryConfig"
        :loading="loading1"
        :show-index="true"
        :showActions="true"
        :merge-keys="['productionName']"
        :height="600"
        @cell-click="exInputReason"
        @save-row="(row) => saveRow(row, 'ex')"
      >
        <template #reason="{ row, $index }">
          {{ row.reason || "无" }}
        </template>
        <template #actions="{ row, $index }">
          <el-button
            type="text"
            size="small"
            @click="saveRow(row, (type = 'ex'))"
            >保存</el-button
          >
        </template>
      </OgwTable>
      <div class="table-title">
        陵水-座城作业公司药剂服务费用执行(截止{{ date }})
      </div>
      <OgwTable
      :loading="loading2"
        :data="serTableData"
        :columns="serColumns"
        :summary-config="serSummaryConfig"
        :show-index="true"
        :merge-keys="['productionName']"
        :showActions="true"
        @cell-click="serInputReason"
        @save-row="(row) => saveRow(row, 'ser')"
      >
        <template #reason="{ row, $index }">
          {{ row.reason || "无" }}
        </template>
        <template #actions="{ row, $index }">
          <el-button
            type="text"
            size="small"
            @click="saveRow(row, (type = 'ser'))"
            >保存</el-button
          >
        </template>
      </OgwTable>
      <div class="table-title">
        陵水-座城作业公司分析化验费用(截止{{ date }})
      </div>
      <OgwTable
        :loading="loading3"
        :data="testTableData"
        :columns="testColumns"
        :summary-config="testSummaryConfig"
        :show-index="true"
        :merge-keys="['productionName']"
        :showActions="true"
        @cell-click="testInputReason"
        @save-row="(row) => saveRow(row, 'test')"
      >
        <template #reason="{ row, $index }">
          {{ row.reason || "无" }}
        </template>
        <template #actions="{ row, $index }">
          <el-button
            type="text"
            size="small"
            @click="saveRow(row, (type = 'test'))"
            >保存</el-button
          >
        </template>
      </OgwTable>
    </div>
    <el-dialog :visible.sync="showInput" v-if="showInput" title="执行差异原因">
      <el-input v-model.trim="reasonInfo" type="textarea" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="showInput = false">取 消</el-button>
        <el-button type="primary" @click="confirmInput">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import { getToday } from "@/utils/index";
import {
  getExecutExpenses,
  getServiceFee,
  getTestFee,
  saveExecutExpenses,
  saveServiceFee,
  saveTestFee,
} from "@/api/ogwWatch/costStatistics.js";
import { getProd } from "@/api/common.js";
export default {
  name: "costStatistics",
  components: {
    OgwSearch,
    OgwTable,
  },
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
    this.searchForm.endTime = getToday().YM2;
    this.getProdList();
    this.getExTableData();
    this.getSerTableData();
    this.getTestTableData();
  },
  watch: {
    searchForm: {
      handler(val) {
        this.deviceOptions = this.orgList.map((item) => {
          if (item.orgId === val.orgId) {
            return item.children;
          }
        });
        this.searchFields[1].options = this.deviceOptions.flat(Infinity);
      },
      deep: true,
    },
  },
  computed: {
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [],
        },
        {
          label: "截止",
          prop: "endTime",
          type: "month",
        },
      ];
    },
    date() {
      return this.searchForm.endTime;
    },
    exColumns() {
      const year = this.searchForm.endTime.split("-")[0];
      const lastMonth = Number(this.searchForm.endTime.split("-")[1]);

      return [
        { label: "平台名称", prop: "productionName" },
        { label: "药剂型号", prop: "chemicalType" },
        {
          label: `${year}年度1-${lastMonth}月实际消耗(升)`,
          prop: "injectionVolumeSum",
        },
        { label: `${year}年预算金额(万元)`, prop: "budgetcostytd" },
        {
          label: `${year}年1-${lastMonth}月预算金额(万元)`,
          prop: "budgetcostmonth",
        },
        {
          label: `${year}年1-${lastMonth}月实际金额(万元)`,
          prop: "finalcostmonth",
        },
        {
          label: `${year}年1-${lastMonth}月药剂出库费用(万元)`,
          prop: "outboundCost",
          editable: true,
        },
        {
          label: "处理气量(亿方)",
          prop: "airVolume",
        },
        {
          label: "处理水量(方)",
          prop: "waterVolume",
        },
        {
          label: "处理油量(方)",
          prop: "oilVolume",
        },
        {
          label: "推荐药剂应加注浓度(ppm)",
          prop: "reConcentration",
        },
        {
          label: "药剂实际加注浓度(ppm)",
          prop: "concentration",
        },
        {
          label: `${year - 1}年同期药剂实际加注浓度`,
          prop: "concentrationLastYear",
        },
        {
          label: "执行差异说明",
          prop: "reason",
          clickable: true,
        },
      ];
    },
    serColumns() {
      const headTitle = `预算金额(截止${this.searchForm.endTime})`;
      return [
        { label: "平台名称", prop: "productionName" },
        { label: "服务项目", prop: "projectName" },
        {
          label: "服务时间",
          prop: "serviceTime",
          formatter: (row) => {
            return row.serviceTime.split(" ")[0];
          },
        },
        {
          label: `${headTitle}`,
          prop: "budgetCost",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入有效的金额",
          },
        },
        {
          label: "已执行金额(万元)",
          prop: "finalCost",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入有效的金额",
          },
        },
        {
          label: "执行差异原因",
          prop: "reason",
          clickable: true,
        },
      ];
    },
    testColumns() {
      const headTitle = `预算金额(截止${this.searchForm.endTime})`;
      return [
        { label: "平台名称", prop: "productionName" },
        { label: "项目名称", prop: "projectName" },
        {
          label: "服务时间",
          prop: "serviceTime",
          formatter: (row) => {
            return row.serviceTime.split(" ")[0];
          },
        },
        {
          label: `${headTitle}`,
          prop: "budgetCost",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入有效的金额",
          },
        },
        {
          label: "已执行金额(万元)",
          prop: "finalCost",
          editable: true,
          validation: {
            type: "decimal",
            required: true,
            precision: 2,
            errorMessage: "请输入有效的金额",
          },
        },
        {
          label: "执行差异原因",
          prop: "reason",
          clickable: true,
        },
      ];
    },
  },
  data() {
    return {
      serTotal: {},
      exTotal: {},
      loading1: false,
      loading2: false,
      loading3: false,
      orgList: [],
      deviceOptions: [],
      showInput: false,
      reasonInfo: "",
      reasonRowId: "", //记录是哪行表格数据
      reasonType: "", //记录是哪个表格
      searchForm: {
        orgId: "",
        deviceNameCode: "",
        endTime: "",
      },
      exTableData: [],
      exSummaryConfig: {
        groupField: "productionName", // 分组字段
        sumColumns: ["injectionVolumeSum", "budgetcostytd"], // 需要计算的列
        grandTotalText: "合计",
        subTotalText: "小计",
        customSubTotalText: "小计(sap)",
        customGrandTotalText: "总计(sap)",
        showSubTotal: true, // 是否显示小计
        showGrandTotal: true, // 是否显示总计
        showCustomSubTotal: true,
        showCustomGrandTotal: true, // 是否显示自定义总计
        subTotalTextMergeColumns: ["chemicalType"], // 小计合并的列
        customSubTotalTextMergeColumns: ["chemicalType"],
        grandTotalTextMergeColumns: ["productionName", "chemicalType"], // 总计合并的列
        customGrandTotalTextMergeColumns: ["productionName", "chemicalType"],
        customSubTotalData: (g, r) => {
          return {
            deviceNameType: "自定义小计",
            primaryDrugModel: "A",
          };
        },
        customGrandTotalData: (g, r) => {
          return {
            deviceNameType: "自定义总计",
            primaryDrugModel: "A",
          };
        },
      },
      serTableData: [],
      serSummaryConfig: {
        groupField: "productionName",
        sumColumns: ["budgetCost", "finalCost"],
        grandTotalText: "合计",
        subTotalText: "小计",
        customSubTotalText: "小计(sap)",
        customGrandTotalText: "总计(sap)",
        showSubTotal: true,
        showGrandTotal: true,
        showCustomSubTotal: true,
        showCustomGrandTotal: true,
        subTotalTextMergeColumns: ["projectName", "serviceTime"],
        customSubTotalTextMergeColumns: ["projectName", "serviceTime"],
        grandTotalTextMergeColumns: [
          "productionName",
          "projectName",
          "serviceTime",
        ],
        customGrandTotalTextMergeColumns: [
          "productionName",
          "projectName",
          "serviceTime",
        ],
        customSubTotalData: (group, row) => {
          const customData = row?.customData || {};
          return {
            budgetCost: customData.budgetCost || 0,
            finalCost: customData.finalCost || 0,
          };
        },
        customGrandTotalData: () => {
          return {
            budgetCost: this.serTotal.budget || 0,
            finalCost: this.serTotal.final || 0,
          };
        },
      },
      testTableData: [],
      testSummaryConfig: {
        groupField: "productionName",
        sumColumns: ["budgetCost", "finalCost"],
        grandTotalText: "合计",
        subTotalText: "小计",
        customSubTotalText: "小计(sap)",
        customGrandTotalText: "总计(sap)",
        showSubTotal: true,
        showGrandTotal: true,
        showCustomSubTotal: true,
        showCustomGrandTotal: true,
        subTotalTextMergeColumns: ["projectName", "serviceTime"],
        customSubTotalTextMergeColumns: ["projectName", "serviceTime"],
        grandTotalTextMergeColumns: [
          "productionName",
          "projectName",
          "serviceTime",
        ],
        customGrandTotalTextMergeColumns: [
          "productionName",
          "projectName",
          "serviceTime",
        ],
        customSubTotalData: (group, row) => {
          const customData = row?.customData || {};
          return {
            budgetCost: customData.budgetCost || 0,
            finalCost: customData.finalCost || 0,
          };
        },
        customGrandTotalData: (allData) => {
          return {
            budgetCost: this.testTotal.budget || 0,
            finalCost: this.testTotal.final || 0,
          };
        },
      },
    };
  },
  methods: {
    exInputReason({ row, prop, index, value }) {
      // console.log("exInputReason", value);
      this.showInput = true;
      this.reasonInfo = value;
      this.reasonType = "ex";
    },
    serInputReason({ row, prop, index, value }) {
      console.log('serInputReason', row, prop, index, value);
      this.reasonRowId = row.serveId;
      this.showInput = true;
      this.reasonInfo = value;
      this.reasonType = "ser";
    },
    testInputReason({ row, prop, index, value }) {
      this.reasonRowId = row.serveId;
      this.showInput = true;
      this.reasonInfo = value;
      this.reasonType = "test";
    },
    confirmInput() {
      this.showInput = false;
      switch (this.reasonType) {
        case "ex":
          
          break;
        case "ser":
          // 给对应行的reason赋值
          this.serTableData.forEach((item) => {
            if (item.serveId === this.reasonRowId) {
              item.reason = this.reasonInfo;
            }
          })
          break;
        case "test":
          this.testTableData.forEach((item) => {
            if (item.serveId === this.reasonRowId) {
              item.reason = this.reasonInfo;
            }
          })
          break;
      }
    },
    handleSearch(value) {
      this.searchForm.deptId = value?.deptId;
      this.searchForm.deviceNameCode = value?.deviceNameCode;
      this.searchForm.endTime = value.endTime;
      this.getExTableData();
      this.getSerTableData();
      this.getTestTableData();
    },
    handleReset(value) {
      // 重置搜索表单
      this.searchForm = {
        deptId: "",
        deviceNameCode: "",
        endTime: "",
      };
      this.getExTableData();
      this.getSerTableData();
      this.getTestTableData();
    },
    async saveRow(row, type) {
      switch (type) {
        case "ex":
          const resE = await saveExecutExpenses(row);
          if (resE.code === 200) {
            this.$message.success("保存成功");
            this.getExTableData();
          }
          break;
        case "ser":
          row.id = row.serveId
          const resS = await saveServiceFee(row);
          if (resS.code === 200) {
            this.$message.success("保存成功");
            this.getSerTableData();
          }
          break;
        case "test":
          row.id = row.serveId
          const resT = await saveTestFee(row);
          if (resT.code === 200) {
            this.$message.success("保存成功");
            this.getTestTableData();
          }
          break;
      }
    },
    async getExTableData() {
      this.loading1 = true;
      try {
        const data = {
          productionUnitIds: this.searchForm.deviceNameCode || null,
          endDate: this.searchForm.endTime || null,
        };
        const res = await getExecutExpenses(data);
        if (res.code === 200) {
          this.exTableData = res.data;
        }
      } catch (error) {
        console.error("获取数据失败:", error);
        this.$message.error("数据加载失败，请稍后重试");
      } finally {
        this.loading1 = false;
      }
    },
    async getSerTableData() {
      this.loading2 = true;
      try {
        const data = {
          productionUnitIds: this.searchForm.deviceNameCode || null,
          endDate: this.searchForm.endTime || null,
        };
        const res = await getServiceFee(data);
        if (res.code === 200) {
          // 获取分组数据的budget总计和final总计(传入的budget可能是""或null)
          this.serTotal = res.data.reduce(
            (acc, cur) => {
              acc.budget += cur.budget || 0;
              acc.final += cur.final || 0;
              return acc;
            },
            { budget: 0, final: 0 }
          );
          this.serTableData = res.data.map((item) => {
            item.list.forEach((i) => {
              i.customData = {
                budgetCost: item.budget,
                finalCost: item.final,
              };
            });
            return item.list;
          });
        }
        this.serTableData = this.serTableData.flat();
      } catch (error) {
        console.error("获取数据失败:", error);
        this.$message.error("数据加载失败");
      }finally {
        this.loading2 = false;
      }
    },
    async getTestTableData() {
      this.loading3 = true;
      try {
        const data = {
          productionUnitIds: this.searchForm.deviceNameCode || null,
          endDate: this.searchForm.endTime || null,
        };
        const res = await getTestFee(data);
        if (res.code === 200) {
          // 获取分组数据的budget总计和final总计(传入的budget可能是""或null)
          this.testTotal = res.data.reduce(
            (acc, cur) => {
              acc.budget += cur.budget || 0;
              acc.final += cur.final || 0;
              return acc;
            },
            { budget: 0, final: 0 }
          );
          this.testTableData = res.data.map((item) => {
            item.list.forEach((i) => {
              i.customData = {
                budgetCost: item.budget,
                finalCost: item.final,
              };
            });
            return item.list;
          });
        }
        this.testTableData = this.testTableData.flat();
      } catch (error) {
        console.error("获取数据失败:", error);
        this.$message.error("数据加载失败");
      }finally {
        this.loading3 = false;
      }
    },
    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.name,
          })),
        }));
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.cost-statistics {
  padding: 0 16px;
  padding-bottom: 20px;
  background-color: #fff;

  .search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .title {
    font-size: 22px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
    color: #000;
  }

  .table-title {
    padding-top: 10px;
    font-size: 16px;
    text-align: center;
    color: #000;
  }
}
</style>
